"use client";

import { Input as LInput, Typography as LTypography, Radio as LRadio, RadioGroup as LRadioGroup } from "@design-systems/apollo-ui";
import { Badge, Button, DateInput, Input, Tabs, Typography, } from "@apollo/ui"
import { useState } from "react";
import { Search } from "@design-systems/apollo-icons"

const page = () => {
    const [dateType, setDateType] = useState("year")
    const [date, setDate] = useState<Date | null>(new Date())

    return (
        <div className="flex flex-col items-start gap-[16px]">
            <LTypography level="h1">รายการ Promotion Period</LTypography>
            <LTypography level="body-1">รายการ Promotion Period ทั้งหมดที่ถูกสร้าง</LTypography>
            <div className="flex flex-col gap-[16px] items-start p-[24px] bg-surface-static-default1 w-full">
                <div className="flex flex-col gap-[16px] items-start p-[24px] bg-white w-full border border-border-default rounded-lg border-solid">
                    <div style={{ display: 'flex', gap: 16, width: '100%', alignItems: 'baseline' }}>
                        <LInput placeholder="Type here !" label="ชื่อ Promotion Period" fullWidth />
                        <Input placeholder="Type here !" label="ชื่อ Promotion Period" fullWidth />
                        <div className="flex flex-col w-full">
                            <DateInput label={
                                <LRadioGroup
                                    value={dateType}
                                    direction="horizontal"
                                    onChange={(value) => setDateType(value)}
                                >
                                    <LRadio label="ตามปี" value="year" />
                                    <LRadio label="ตามช่วงวันที่" value="date" />
                                </LRadioGroup>
                            } value={date} showYearPicker={dateType === "year"} format={dateType === "year" ? "yyyy" : "dd/MM/yyyy"} onChange={(value) => setDate(value)} fullWidth />
                        </div>
                    </div>
                    <div style={{ display: 'flex', gap: 16, width: '100%', justifyContent: 'flex-end' }}>
                        <Button variant="text">ล้างค่า</Button>
                        <Button startDecorator={<Search />}>ค้นหา</Button>
                    </div>
                </div>
                <div style={{ display: 'flex', gap: 16, width: '100%', justifyContent: 'flex-start' }}>
                    <Typography level="h2">คำค้นหา:</Typography>
                    <Badge label={`${dateType === "year" ? "ปี" : "ช่วงวันที่"}: ${dateType === "year" ? date.getFullYear() : date.toLocaleDateString()}`} color="success" />
                </div>
                
                <Tabs.Root defaultValue="all">
                    <Tabs.List>
                        <Tabs.Tab value="all">ทั้งหมด (74)</Tabs.Tab>
                        <Tabs.Tab value="active">Active (40)</Tabs.Tab>
                        <Tabs.Indicator />
                    </Tabs.List>
                    <Tabs.Panel value="all">Tab 1 Content</Tabs.Panel>
                    <Tabs.Panel value="active">Tab 2 Content</Tabs.Panel>
                </Tabs.Root>
            </div>
        </div>
    )
}

export default page