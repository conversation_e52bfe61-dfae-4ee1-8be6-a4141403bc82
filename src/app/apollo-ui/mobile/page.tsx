"use client";

import { useRouter } from "next/navigation";
import { useState } from "react";

// Icon components
const ServiceIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
  </svg>
);

const CouponIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
  </svg>
);

const RewardsIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
  </svg>
);

const HomeIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
  </svg>
);

const SearchIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
  </svg>
);

const RedeemIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
  </svg>
);

const HistoryIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
  </svg>
);

const ProfileIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
  </svg>
);

// Mock data for products
const mockProducts = [
  {
    id: 1,
    name: "กระเป้าลูกฟูก",
    price: 89,
    originalPrice: null,
    image: "/api/placeholder/150/150",
    deadline: "30 มิ.ย. 2568, 23:59"
  },
  {
    id: 2,
    name: "รองเท้าแตะ Ponee",
    price: 119,
    originalPrice: null,
    image: "/api/placeholder/150/150",
    deadline: "30 มิ.ย. 2568, 23:59"
  },
  {
    id: 3,
    name: "พัดลมมือถือ",
    price: 159,
    originalPrice: null,
    image: "/api/placeholder/150/150",
    deadline: "30 มิ.ย. 2568, 23:59"
  },
  {
    id: 4,
    name: "เครื่องหนีบจัดกรอมผม Hul...",
    price: 2500,
    originalPrice: null,
    image: "/api/placeholder/150/150",
    deadline: "30 มิ.ย. 2568, 23:59"
  }
];

// Tab data
const tabs = [
  { id: 'service', label: 'บริการ', icon: ServiceIcon },
  { id: 'coupon', label: 'คูปอง', icon: CouponIcon },
  { id: 'rewards', label: 'ของรางวัล', icon: RewardsIcon }
];

// Bottom navigation data
const bottomNavItems = [
  { id: 'home', label: 'หน้าหลัก', icon: HomeIcon },
  { id: 'search', label: 'ค้นหาของ', icon: SearchIcon },
  { id: 'redeem', label: 'แลกแต้ม', icon: RedeemIcon },
  { id: 'history', label: 'คู่มือของดี', icon: HistoryIcon },
  { id: 'profile', label: 'ฉัน', icon: ProfileIcon }
];

export default function ApolloUIMobilePage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('rewards');
  const [activeBottomTab, setActiveBottomTab] = useState('redeem');

  return (
    <div className="min-h-screen bg-white flex flex-col">
      {/* Top Navigation Bar */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <button
            onClick={() => router.back()}
            className="p-2 -ml-2"
          >
            <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <div className="flex-1 mx-3">
            <div className="bg-gray-100 rounded-full px-3 py-2 text-center">
              <span className="text-sm text-gray-600">ของรางวัล</span>
              <div className="text-xs text-gray-500 mt-1">https://www.cjexpress.co.th</div>
            </div>
          </div>

          <button className="p-2 -mr-2">
            <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white border-b border-gray-200">
        <div className="flex">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex-1 py-4 px-2 text-center border-b-2 transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500'
              }`}
            >
              <div className="flex flex-col items-center space-y-1">
                <tab.icon />
                <span className="text-sm font-medium">{tab.label}</span>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Points Display */}
      <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <span className="text-gray-700 font-medium">แต้มสะสมของคุณ: 1,740</span>
          <button className="p-1">
            <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>
      </div>

      {/* Product Grid */}
      <div className="flex-1 p-4 pb-20">
        <div className="grid grid-cols-2 gap-4">
          {mockProducts.map((product) => (
            <div key={product.id} className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
              {/* Product Image */}
              <div className="aspect-square bg-gray-100 relative">
                <div className="absolute inset-0 flex items-center justify-center">
                  {product.id === 1 && (
                    <div className="w-24 h-16 bg-blue-200 rounded-lg flex items-center justify-center">
                      <span className="text-xs text-blue-600">กระเป้า</span>
                    </div>
                  )}
                  {product.id === 2 && (
                    <div className="w-20 h-12 bg-orange-200 rounded-full flex items-center justify-center">
                      <span className="text-xs text-orange-600">รองเท้า</span>
                    </div>
                  )}
                  {product.id === 3 && (
                    <div className="w-16 h-20 bg-cyan-200 rounded-lg flex items-center justify-center">
                      <span className="text-xs text-cyan-600">พัดลม</span>
                    </div>
                  )}
                  {product.id === 4 && (
                    <div className="w-20 h-8 bg-pink-200 rounded-lg flex items-center justify-center">
                      <span className="text-xs text-pink-600">เครื่องหนีบ</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Product Info */}
              <div className="p-3">
                <h3 className="text-sm font-medium text-gray-900 mb-2 line-clamp-2">
                  {product.name}
                </h3>

                <div className="flex items-center justify-between mb-3">
                  <span className="text-lg font-bold text-gray-900">
                    {product.price} แต้ม
                  </span>
                </div>

                <div className="text-xs text-red-500 mb-3">
                  แลกได้ถึง {product.deadline}
                </div>

                <button className="w-full bg-green-600 text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors">
                  แลกสินค้า
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div className="flex">
          {bottomNavItems.map((item) => (
            <button
              key={item.id}
              onClick={() => setActiveBottomTab(item.id)}
              className={`flex-1 py-3 px-2 text-center transition-colors ${
                activeBottomTab === item.id
                  ? 'text-blue-600'
                  : 'text-gray-500'
              }`}
            >
              <div className="flex flex-col items-center space-y-1">
                <item.icon />
                <span className="text-xs font-medium">{item.label}</span>
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}