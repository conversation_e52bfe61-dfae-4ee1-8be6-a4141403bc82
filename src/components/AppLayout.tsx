"use client";

import {
  Sidebar,
} from "@design-systems/apollo-ui";

import {
  Home as HomeIcon,
  Calendar,
  Logout as LogoutIcon
} from "@design-systems/apollo-icons";

import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import Logo from "@/icons/Logo";

const getParentPath = (path: string) => {
  if (path.startsWith('/design-systems-apollo-ui')) return "/design-systems-apollo-ui"
  else if (path.startsWith('/combine')) return "/combine"
  return null
}
export default function Layout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()
  const currentParentPath = getParentPath(pathname)
  const [sidebarCollapsed, setSidebarCollapsed] = useState<boolean>(false)
  const [selectedMenuKey, setSelectedMenuKey] = useState<string | number>("home")


  // Handle client-side mounting and restore sidebar state
  useEffect(() => {
    // Restore sidebar collapsed state from localStorage (only on client-side)
    if (typeof window !== 'undefined') {
      const savedCollapsedState = localStorage.getItem('sidebar-collapsed')
      if (savedCollapsedState !== null) {
        setSidebarCollapsed(JSON.parse(savedCollapsedState))
      }
    }
  }, [sidebarCollapsed])

  // Update selected menu key when pathname changes
  useEffect(() => {
    const parentPath = getParentPath(pathname)

    const menuKey = pathname.replace(parentPath ? parentPath : "/", "").replace("/", "") || "home"
    setSelectedMenuKey(menuKey)

    console.log("🔄 Route changed to:", pathname, "→ Menu key:", menuKey)
  }, [pathname])


  // Handle sidebar collapse/expand and persist state
  const handleSidebarCollapse = (collapsed: boolean) => {
    setSidebarCollapsed(collapsed)
    // Save to localStorage to persist across page navigation (only on client-side)
    if (typeof window !== 'undefined') {
      localStorage.setItem('sidebar-collapsed', JSON.stringify(collapsed))
    }
    console.log("🔧 Sidebar", collapsed ? "collapsed" : "expanded")
  }

  return (
    pathname.startsWith('/design-systems-apollo-ui') || pathname.startsWith('/combine') ? <div className="flex h-screen">
      {/* eslint-disable-next-line react/jsx-key, react/jsx-props-no-spreading */}
      {/* Apollo UI Sidebar component spreads props containing 'key' internally - library limitation */}
      <Sidebar
        collapsible
        collapsed={sidebarCollapsed}
        onCollapsedChange={handleSidebarCollapse}
        title="PMS"
        logo={
          <Logo />
        }
        menus={[
          {
            key: "main-section",
            label: "แฟ้มหลัก", // Main Files
            items: [
              {
                key: "home",
                label: "หน้าแรก", // Home
                icon: <HomeIcon size={20} />,
                href: currentParentPath ? currentParentPath : "/",
              },
              {
                key: "promotions",
                label: "รอบโปรโมชั่น", // Promotion Rounds
                icon: <Calendar size={20} />,
                href: (currentParentPath ? currentParentPath : "") + "/promotions",
              },
            ],
          },
        ]}
        selectedMenuKey={selectedMenuKey}
        // expandedMenuKeys={expandedMenuKeys}
        footer={[
          {
            key: "logout",
            label: "ออกจากระบบ", // Logout
            icon: <LogoutIcon size={20} />,
            onClick: () => console.log("Logout clicked"),
          },
        ]}
      />

      {/* Page content will go here */}
      <div className="flex-1 p-6 bg-surface-static-default2 overflow-auto">
        {children}
      </div>
    </div> : children
  );
}
