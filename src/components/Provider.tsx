"use client";

import { Theme<PERSON><PERSON><PERSON>, ToastProvider, createTheme } from "@design-systems/apollo-ui";
import { ApolloProvider } from "@apollo/ui";
import { usePathname } from 'next/navigation';

const Provider = ({ children }: { children: React.ReactNode }) => {
    const pathname = usePathname();
    const themeProviderProps = pathname.startsWith('/design-systems-apollo-ui')
        ? { theme: createTheme(), children: <ToastProvider>{children}</ToastProvider> }
        : pathname.startsWith('/combine')
            ? { theme: createTheme(), children: <ToastProvider><ApolloProvider>{children}</ApolloProvider></ToastProvider> }
            : null;

    return (
        <>
            {pathname.startsWith('/apollo/ui') ? <ApolloProvider>{children}</ApolloProvider> : themeProviderProps !== null ? <ThemeProvider {...themeProviderProps}>{children}</ThemeProvider> : children}
        </>
    )
}
export default Provider